html {
  font-size: 14px;
}

@media (min-width: 768px) {
  html {
    font-size: 16px;
  }
}

body {
  font-family: 'Poppins', sans-serif;
  background-color: #f4f7f6; /* Softer background color */
  color: #333;
  line-height: 1.6;
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

.container, .container-fluid {
    max-width: 1400px; /* Limit overall width for better readability on large screens */
}

/* Header & Navbar */
.navbar {
  padding-top: 0.8rem;
  padding-bottom: 0.8rem;
  transition: box-shadow 0.3s ease-in-out;
}

.navbar-brand {
  font-size: 1.5rem; /* Slightly larger brand text */
}

.navbar .nav-link {
  color: #555;
  font-weight: 500;
  padding: 0.6rem 1rem;
  border-radius: 0.375rem; /* Bootstrap's default rounded-md */
  transition: background-color 0.2s ease-in-out, color 0.2s ease-in-out;
}

.navbar .nav-link:hover,
.navbar .nav-link.active {
  color: #0d6efd; /* Primary color for active/hover links */
  background-color: rgba(13, 110, 253, 0.05); /* Light primary background on hover */
}

.navbar .dropdown-menu {
    border: none;
    box-shadow: 0 0.5rem 1rem rgba(0,0,0,.15); /* Standard Bootstrap shadow */
    border-radius: 0.5rem; /* Slightly more rounded dropdown */
}

.navbar .dropdown-item {
    padding: 0.75rem 1.25rem;
    font-weight: 500;
}

.navbar .dropdown-item:hover {
    background-color: rgba(13, 110, 253, 0.05);
    color: #0d6efd;
}

.navbar-toggler {
    border-color: rgba(0,0,0,0.1);
}

.navbar-toggler-icon {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%280, 0, 0, 0.55%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e");
}


/* Main Content Area */
main {
  flex-grow: 1;
  padding-top: 1.5rem;
  padding-bottom: 3rem;
}

/* Card Styles */
.card {
  border: none;
  border-radius: 0.75rem; /* More pronounced rounding */
  box-shadow: 0 0.125rem 0.25rem rgba(0,0,0,0.04), 0 0.5rem 1.5rem rgba(0,0,0,0.08); /* Softer, layered shadow */
  margin-bottom: 1.5rem;
  background-color: #fff;
  transition: transform 0.2s ease-out, box-shadow 0.2s ease-out;
}

.card:hover {
  transform: translateY(-3px);
  box-shadow: 0 0.25rem 0.5rem rgba(0,0,0,0.06), 0 0.75rem 2rem rgba(0,0,0,0.1);
}

.card-header {
  background-color: transparent; /* Cleaner header */
  border-bottom: 1px solid #e9ecef; /* Lighter border */
  padding: 1rem 1.5rem;
  font-size: 1.125rem;
  font-weight: 600;
  color: #343a40;
}

.card-body {
  padding: 1.5rem;
}

.card-footer {
    background-color: #f8f9fa;
    border-top: 1px solid #e9ecef;
    padding: 1rem 1.5rem;
}

/* Form Elements */
.form-label {
  font-weight: 500;
  margin-bottom: 0.5rem;
  color: #495057;
}

.form-control, .form-select {
  border-radius: 0.375rem; /* Bootstrap's default rounded-md */
  border: 1px solid #ced4da;
  padding: 0.75rem 1rem;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  background-color: #fff; /* Ensure form controls have white background */
}

.form-control:focus, .form-select:focus {
  border-color: #86b7fe; /* Bootstrap's focus color */
  box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25); /* Bootstrap's focus shadow */
}

.input-group-text {
  background-color: #e9ecef; /* Lighter input group background */
  border: 1px solid #ced4da;
  border-radius: 0.375rem;
}

/* Buttons */
.btn {
  padding: 0.6rem 1.2rem;
  font-weight: 500;
  border-radius: 0.375rem;
  transition: all 0.2s ease-in-out;
  letter-spacing: 0.5px; /* Subtle letter spacing for modern feel */
}

.btn-primary {
  background-color: #0d6efd;
  border-color: #0d6efd;
}
.btn-primary:hover {
  background-color: #0b5ed7;
  border-color: #0a58ca;
  transform: translateY(-1px);
}

.btn-secondary {
  background-color: #6c757d;
  border-color: #6c757d;
}
.btn-secondary:hover {
  background-color: #5c636a;
  border-color: #565e64;
  transform: translateY(-1px);
}

.btn-success {
  background-color: #198754;
  border-color: #198754;
}
.btn-success:hover {
  background-color: #157347;
  border-color: #146c43;
  transform: translateY(-1px);
}

.btn-danger {
  background-color: #dc3545;
  border-color: #dc3545;
}
.btn-danger:hover {
  background-color: #bb2d3b;
  border-color: #b02a37;
  transform: translateY(-1px);
}

.btn-warning {
  background-color: #ffc107;
  border-color: #ffc107;
  color: #000;
}
.btn-warning:hover {
  background-color: #ffca2c;
  border-color: #ffc720;
  transform: translateY(-1px);
}

.btn-info {
  background-color: #0dcaf0;
  border-color: #0dcaf0;
  color: #000;
}
.btn-info:hover {
  background-color: #31d2f2;
  border-color: #25cff2;
  transform: translateY(-1px);
}

.btn-light {
  background-color: #f8f9fa;
  border-color: #f8f9fa;
  color: #000;
}
.btn-light:hover {
  background-color: #d3d4d5;
  border-color: #c6c7c8;
  transform: translateY(-1px);
}

.btn-dark {
  background-color: #212529;
  border-color: #212529;
}
.btn-dark:hover {
  background-color: #1c1f23;
  border-color: #1a1e21;
  transform: translateY(-1px);
}

.btn-link {
    font-weight: 500;
}

.btn-sm {
  padding: 0.4rem 0.8rem;
  font-size: 0.875rem;
}

.btn-lg {
  padding: 0.8rem 1.5rem;
  font-size: 1.125rem;
}

/* Tables */
.table {
  border-radius: 0.5rem; /* Rounded corners for the table wrapper if any */
  overflow: hidden; /* Ensures child elements adhere to border-radius */
  margin-bottom: 1rem;
  background-color: #fff; /* Ensure table has white background */
  box-shadow: 0 0.125rem 0.25rem rgba(0,0,0,0.04); /* Subtle shadow for tables */
}

.table thead th {
  background-color: #f8f9fa; /* Light header for tables */
  border-bottom: 2px solid #dee2e6;
  font-weight: 600;
  color: #495057;
  padding: 0.9rem 1rem;
  vertical-align: middle;
}

.table tbody td {
  padding: 0.9rem 1rem;
  vertical-align: middle;
  border-top: 1px solid #e9ecef; /* Lighter row separators */
}

.table-hover tbody tr:hover {
  background-color: rgba(13, 110, 253, 0.03); /* Very subtle hover for table rows */
}

.table-striped tbody tr:nth-of-type(odd) {
    background-color: rgba(0,0,0,.02); /* Lighter stripe */
}

.table-bordered {
    border: 1px solid #dee2e6;
}
.table-bordered th,
.table-bordered td {
    border: 1px solid #dee2e6;
}

/* Alerts */
.alert {
  border: none;
  border-radius: 0.5rem; /* Consistent rounding */
  padding: 1rem 1.25rem;
  margin-bottom: 1.5rem;
  box-shadow: 0 0.25rem 0.75rem rgba(0,0,0,0.08); /* Soft shadow for alerts */
}

.alert-heading {
    font-weight: 600;
}

.alert-dismissible .btn-close {
    padding: 1.15rem 1.25rem; /* Better touch target for close button */
}

/* Footer */
.footer {
  padding: 1.5rem 0;
  font-size: 0.9rem;
  background-color: #fff; /* White footer for cleaner look */
}

/* Utility Classes */
.shadow-sm { box-shadow: 0 .125rem .25rem rgba(0,0,0,.075)!important; }
.shadow { box-shadow: 0 .5rem 1rem rgba(0,0,0,.15)!important; }
.shadow-lg { box-shadow: 0 1rem 3rem rgba(0,0,0,.175)!important; }

.fw-medium { font-weight: 500 !important; }
.fw-semibold { font-weight: 600 !important; }

.text-muted-light { color: #868e96 !important; } /* Lighter muted text */

/* Authentication Page Specific Styles */
.auth-page-background {
    background-color: #e9ecef; /* A slightly different background for auth pages */
    display: flex;
    align-items: center; /* Vertically center content */
    justify-content: center; /* Horizontally center content */
    padding-top: 0; /* Remove default body padding */
    padding-bottom: 0;
}

.auth-container {
    width: 100%;
    max-width: none; /* Allow auth card to define its own max-width */
    padding-left: 15px; /* Standard container padding */
    padding-right: 15px;
}

.auth-main {
    padding-top: 0; /* Remove main padding for auth pages */
    padding-bottom: 0;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.auth-card {
    width: 100%; /* Card takes full width of its column */
    /* max-width is set in Login.cshtml and Register.cshtml column classes e.g. col-lg-4 */
    margin-top: 2rem; /* Add some top margin */
    margin-bottom: 2rem; /* Add some bottom margin */
    border: none; /* Remove border if any from general card style */
}

.auth-card .card-header {
    text-align: center;
    padding-top: 1.5rem;
    padding-bottom: 1.5rem;
    border-bottom: none; /* Remove border if header is distinct */
}

.auth-card .card-body {
    /* Padding is already set in Login.cshtml and Register.cshtml */
}

.auth-card .navbar-brand { /* If you use brand in auth pages */
    display: block;
    text-align: center;
    margin-bottom: 1rem;
    font-size: 1.75rem; /* Larger brand for auth pages */
    color: #fff; /* Assuming a dark card header */
}

/* Ensure form floating labels are visible on auth pages if background changes */
.auth-page-background .form-floating > .form-control {
    background-color: #fff;
}

.auth-page-background .form-floating > label {
    color: #495057; /* Ensure label color is standard */
}


/* Dashboard Specific Styles */
.dashboard-stat-card {
    color: #fff;
    border-radius: 0.75rem;
    padding: 1.5rem;
    position: relative;
    overflow: hidden;
    margin-bottom: 1.5rem;
    transition: transform 0.2s ease-out, box-shadow 0.2s ease-out;
}

.dashboard-stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 0.5rem 1.5rem rgba(0,0,0,0.15);
}

.dashboard-stat-card .stat-title {
    font-size: 0.9rem;
    opacity: 0.85;
    margin-bottom: 0.25rem;
}

.dashboard-stat-card .stat-value {
    font-size: 2rem;
    font-weight: 600;
}

.dashboard-stat-card .stat-icon {
    position: absolute;
    right: 1.5rem;
    top: 50%;
    transform: translateY(-50%);
    font-size: 3rem;
    opacity: 0.2;
}

.bg-stat-primary { background: linear-gradient(45deg, #0d6efd, #0a58ca); }
.bg-stat-success { background: linear-gradient(45deg, #198754, #146c43); }
.bg-stat-warning { background: linear-gradient(45deg, #ffc107, #d9a400); color: #333 !important; }
.bg-stat-danger  { background: linear-gradient(45deg, #dc3545, #b02a37); }


/* Custom Scrollbar (Optional, for a more modern feel) */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 10px;
}

::-webkit-scrollbar-thumb {
  background: #c5c5c5;
  border-radius: 10px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Responsive Adjustments */
@media (max-width: 991.98px) {
    .navbar-nav {
        margin-top: 0.5rem;
    }
    .navbar .nav-link {
        padding: 0.8rem 1rem; /* Slightly more padding for touch on mobile */
    }
}

@media (max-width: 767.98px) {
  body {
    font-size: 0.95rem; /* Slightly smaller base font on mobile */
  }
  .card-header {
    font-size: 1.05rem;
  }
  .btn {
    padding: 0.5rem 1rem;
    font-size: 0.9rem;
  }
  .dashboard-stat-card .stat-value {
    font-size: 1.75rem;
  }
   .dashboard-stat-card .stat-icon {
    font-size: 2.5rem;
  }
}