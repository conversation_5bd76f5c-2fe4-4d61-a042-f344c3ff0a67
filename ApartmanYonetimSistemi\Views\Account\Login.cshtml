@model LoginViewModel

@{
    ViewData["Title"] = "Giriş Yap";
}

<div class="row justify-content-center align-items-center min-vh-100">
    <div class="col-md-6 col-lg-4">
        <div class="card auth-card shadow-lg">
            <div class="card-header bg-primary text-white text-center py-4">
                <h2 class="mb-0 h4"><i class="bi bi-buildings-fill me-2"></i>Apartman Yönetimi</h2>
            </div>
            <div class="card-body p-4 p-md-5">
                <h3 class="text-center mb-4 fw-semibold">@ViewData["Title"]</h3>
                <form asp-controller="Account" asp-action="Login" method="post">
                    <div asp-validation-summary="ModelOnly" class="alert alert-danger small p-2" role="alert"></div>
                    
                    <div class="form-floating mb-3">
                        <input asp-for="Email" class="form-control" placeholder="<EMAIL>" id="floatingInputEmail" />
                        <label asp-for="Email" for="floatingInputEmail"><i class="bi bi-envelope-fill me-2"></i>E-posta</label>
                        <span asp-validation-for="Email" class="text-danger small"></span>
                    </div>
                    
                    <div class="form-floating mb-3">
                        <input asp-for="Password" class="form-control" placeholder="••••••••" id="floatingPassword" />
                        <label asp-for="Password" for="floatingPassword"><i class="bi bi-lock-fill me-2"></i>Şifre</label>
                        <span asp-validation-for="Password" class="text-danger small"></span>
                    </div>
                    
                    <div class="mb-3 form-check">
                        <input asp-for="RememberMe" class="form-check-input" id="rememberMeCheck" />
                        <label asp-for="RememberMe" class="form-check-label" for="rememberMeCheck">Beni Hatırla</label>
                    </div>
                    
                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary btn-lg">
                            <i class="bi bi-box-arrow-in-right me-2"></i> Giriş Yap
                        </button>
                    </div>
                </form>
                
                <div class="text-center mt-4 pt-3 border-top">
                    <p class="mb-0 small">Hesabınız yok mu? <a asp-controller="Account" asp-action="Register" class="fw-medium text-decoration-none">Hemen Kayıt Olun</a></p>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
} 