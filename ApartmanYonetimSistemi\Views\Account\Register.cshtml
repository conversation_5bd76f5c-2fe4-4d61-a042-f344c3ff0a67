@model RegisterViewModel

@{
    ViewData["Title"] = "Kayıt Ol";
    ViewData["IsAuthPage"] = true;
}

<div class="row justify-content-center align-items-center min-vh-100">
    <div class="col-md-8 col-lg-7 col-xl-6">
        <div class="auth-card-modern">
            <!-- Modern Header -->
            <div class="auth-header">
                <div class="auth-logo">
                    <div class="logo-icon">
                        <i class="bi bi-person-plus-fill"></i>
                    </div>
                    <h2 class="logo-text">Yeni <PERSON>p <PERSON></h2>
                </div>
                <p class="auth-subtitle">Apartman yönetim sistemine katılın</p>
            </div>

            <!-- Form Content -->
            <div class="auth-body">
                <form asp-controller="Account" asp-action="Register" method="post" class="modern-form">
                    <div asp-validation-summary="ModelOnly" class="alert alert-danger modern-alert" role="alert"></div>
                    
                    <div class="row g-3">
                        <div class="col-md-6">
                            <div class="form-group-modern">
                                <div class="input-wrapper">
                                    <i class="bi bi-person-fill input-icon"></i>
                                    <input asp-for="FirstName" class="form-control-modern" placeholder="Adınızı girin" />
                                    <label asp-for="FirstName" class="form-label-modern">Ad</label>
                                </div>
                                <span asp-validation-for="FirstName" class="validation-message"></span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group-modern">
                                <div class="input-wrapper">
                                    <i class="bi bi-person-fill input-icon"></i>
                                    <input asp-for="LastName" class="form-control-modern" placeholder="Soyadınızı girin" />
                                    <label asp-for="LastName" class="form-label-modern">Soyad</label>
                                </div>
                                <span asp-validation-for="LastName" class="validation-message"></span>
                            </div>
                        </div>
                    </div>

                    <div class="form-group-modern">
                        <div class="input-wrapper">
                            <i class="bi bi-envelope-fill input-icon"></i>
                            <input asp-for="Email" class="form-control-modern" placeholder="E-posta adresinizi girin" />
                            <label asp-for="Email" class="form-label-modern">E-posta</label>
                        </div>
                        <span asp-validation-for="Email" class="validation-message"></span>
                    </div>

                    <div class="form-group-modern">
                        <div class="input-wrapper">
                            <i class="bi bi-telephone-fill input-icon"></i>
                            <input asp-for="PhoneNumber" class="form-control-modern" placeholder="Telefon numaranızı girin" />
                            <label asp-for="PhoneNumber" class="form-label-modern">Telefon Numarası</label>
                        </div>
                        <span asp-validation-for="PhoneNumber" class="validation-message"></span>
                    </div>

                    <div class="row g-3">
                        <div class="col-md-6">
                            <div class="form-group-modern">
                                <div class="input-wrapper">
                                    <i class="bi bi-house-door-fill input-icon"></i>
                                    <input asp-for="ApartmentNumber" class="form-control-modern" placeholder="Daire numaranızı girin" type="number" />
                                    <label asp-for="ApartmentNumber" class="form-label-modern">Daire No</label>
                                </div>
                                <span asp-validation-for="ApartmentNumber" class="validation-message"></span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group-modern">
                                <div class="input-wrapper">
                                    <i class="bi bi-building-fill input-icon"></i>
                                    <input asp-for="Block" class="form-control-modern" placeholder="Blok bilgisini girin" />
                                    <label asp-for="Block" class="form-label-modern">Blok</label>
                                </div>
                                <span asp-validation-for="Block" class="validation-message"></span>
                            </div>
                        </div>
                    </div>

                    <div class="form-group-modern">
                        <div class="input-wrapper">
                            <i class="bi bi-lock-fill input-icon"></i>
                            <input asp-for="Password" class="form-control-modern" placeholder="Şifrenizi girin" type="password" />
                            <label asp-for="Password" class="form-label-modern">Şifre</label>
                            <button type="button" class="password-toggle" onclick="togglePasswordRegister('Password')">
                                <i class="bi bi-eye" id="passwordToggleIcon"></i>
                            </button>
                        </div>
                        <span asp-validation-for="Password" class="validation-message"></span>
                    </div>

                    <div class="form-group-modern">
                        <div class="input-wrapper">
                            <i class="bi bi-shield-lock-fill input-icon"></i>
                            <input asp-for="ConfirmPassword" class="form-control-modern" placeholder="Şifrenizi tekrar girin" type="password" />
                            <label asp-for="ConfirmPassword" class="form-label-modern">Şifre Tekrar</label>
                            <button type="button" class="password-toggle" onclick="togglePasswordRegister('ConfirmPassword')">
                                <i class="bi bi-eye" id="confirmPasswordToggleIcon"></i>
                            </button>
                        </div>
                        <span asp-validation-for="ConfirmPassword" class="validation-message"></span>
                    </div>

                    <button type="submit" class="btn-modern btn-primary-modern">
                        <span class="btn-text">Hesap Oluştur</span>
                        <i class="bi bi-arrow-right btn-icon"></i>
                    </button>
                </form>

                <div class="auth-footer">
                    <p class="auth-link-text">
                        Zaten bir hesabınız var mı?
                        <a asp-controller="Account" asp-action="Login" class="auth-link">Giriş Yapın</a>
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}

    <script>
        // Password toggle functionality for register form
        function togglePasswordRegister(fieldName) {
            const passwordInput = document.querySelector(`input[name="${fieldName}"]`);
            const toggleIcon = document.getElementById(fieldName === 'Password' ? 'passwordToggleIcon' : 'confirmPasswordToggleIcon');

            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                toggleIcon.className = 'bi bi-eye-slash';
            } else {
                passwordInput.type = 'password';
                toggleIcon.className = 'bi bi-eye';
            }
        }

        // Modern form interactions
        document.querySelectorAll('.form-control-modern').forEach(input => {
            input.addEventListener('focus', function() {
                this.parentElement.classList.add('focused');
            });

            input.addEventListener('blur', function() {
                if (!this.value) {
                    this.parentElement.classList.remove('focused');
                }
            });

            // Check if input has value on page load
            if (input.value) {
                input.parentElement.classList.add('focused');
            }
        });

        // Form submission animation
        document.querySelector('.modern-form').addEventListener('submit', function(e) {
            const submitBtn = this.querySelector('.btn-modern');
            submitBtn.classList.add('loading');
            submitBtn.querySelector('.btn-text').textContent = 'Hesap oluşturuluyor...';
            submitBtn.querySelector('.btn-icon').className = 'bi bi-arrow-clockwise spin';
        });

        // Real-time password strength indicator
        const passwordInput = document.querySelector('input[name="Password"]');
        const confirmPasswordInput = document.querySelector('input[name="ConfirmPassword"]');

        if (passwordInput) {
            passwordInput.addEventListener('input', function() {
                const password = this.value;
                const strength = calculatePasswordStrength(password);
                showPasswordStrength(strength);
            });
        }

        if (confirmPasswordInput) {
            confirmPasswordInput.addEventListener('input', function() {
                const password = passwordInput.value;
                const confirmPassword = this.value;

                if (confirmPassword && password !== confirmPassword) {
                    this.setCustomValidity('Şifreler eşleşmiyor');
                } else {
                    this.setCustomValidity('');
                }
            });
        }

        function calculatePasswordStrength(password) {
            let strength = 0;
            if (password.length >= 6) strength++;
            if (password.length >= 8) strength++;
            if (/[A-Z]/.test(password)) strength++;
            if (/[a-z]/.test(password)) strength++;
            if (/[0-9]/.test(password)) strength++;
            if (/[^A-Za-z0-9]/.test(password)) strength++;
            return strength;
        }

        function showPasswordStrength(strength) {
            // This could be enhanced with a visual strength indicator
            const strengthTexts = ['Çok Zayıf', 'Zayıf', 'Orta', 'İyi', 'Güçlü', 'Çok Güçlü'];
            const strengthColors = ['#ef4444', '#f59e0b', '#eab308', '#22c55e', '#16a34a', '#15803d'];

            // You can add a strength indicator element here if needed
        }
    </script>
}