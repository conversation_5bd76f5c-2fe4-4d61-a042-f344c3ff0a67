@model RegisterViewModel

@{
    ViewData["Title"] = "Kayıt Ol";
}

<div class="row justify-content-center align-items-center my-5">
    <div class="col-md-8 col-lg-7">
        <div class="card auth-card shadow-lg">
            <div class="card-header bg-primary text-white text-center py-4">
                <h2 class="mb-0 h4"><i class="bi bi-buildings-fill me-2"></i>Apartman Yönetimi - Ye<PERSON></h2>
            </div>
            <div class="card-body p-4 p-md-5">
                <h3 class="text-center mb-4 fw-semibold">@ViewData["Title"]</h3>
                <form asp-controller="Account" asp-action="Register" method="post">
                    <div asp-validation-summary="ModelOnly" class="alert alert-danger small p-2" role="alert"></div>
                    
                    <div class="row g-3">
                        <div class="col-md-6">
                            <div class="form-floating">
                                <input asp-for="FirstName" class="form-control" placeholder="Adınız" id="floatingFirstName" />
                                <label asp-for="FirstName" for="floatingFirstName"><i class="bi bi-person-fill me-2"></i>Ad</label>
                                <span asp-validation-for="FirstName" class="text-danger small"></span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-floating">
                                <input asp-for="LastName" class="form-control" placeholder="Soyadınız" id="floatingLastName" />
                                <label asp-for="LastName" for="floatingLastName"><i class="bi bi-person-fill me-2"></i>Soyad</label>
                                <span asp-validation-for="LastName" class="text-danger small"></span>
                            </div>
                        </div>
                    </div>

                    <div class="form-floating mt-3">
                        <input asp-for="Email" class="form-control" placeholder="<EMAIL>" id="floatingEmail" />
                        <label asp-for="Email" for="floatingEmail"><i class="bi bi-envelope-fill me-2"></i>E-posta</label>
                        <span asp-validation-for="Email" class="text-danger small"></span>
                    </div>

                    <div class="form-floating mt-3">
                        <input asp-for="PhoneNumber" class="form-control" placeholder="05XX XXX XX XX" id="floatingPhone" />
                        <label asp-for="PhoneNumber" for="floatingPhone"><i class="bi bi-telephone-fill me-2"></i>Telefon Numarası</label>
                        <span asp-validation-for="PhoneNumber" class="text-danger small"></span>
                    </div>

                    <div class="row g-3 mt-1">
                        <div class="col-md-6">
                            <div class="form-floating">
                                <input asp-for="ApartmentNumber" class="form-control" placeholder="1" id="floatingApartmentNumber" />
                                <label asp-for="ApartmentNumber" for="floatingApartmentNumber"><i class="bi bi-house-door-fill me-2"></i>Daire No</label>
                                <span asp-validation-for="ApartmentNumber" class="text-danger small"></span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-floating">
                                <input asp-for="Block" class="form-control" placeholder="A" id="floatingBlock" />
                                <label asp-for="Block" for="floatingBlock"><i class="bi bi-building-fill me-2"></i>Blok</label>
                                <span asp-validation-for="Block" class="text-danger small"></span>
                            </div>
                        </div>
                    </div>

                    <div class="form-floating mt-3">
                        <input asp-for="Password" class="form-control" placeholder="••••••••" id="floatingPassword" />
                        <label asp-for="Password" for="floatingPassword"><i class="bi bi-lock-fill me-2"></i>Şifre</label>
                        <span asp-validation-for="Password" class="text-danger small"></span>
                    </div>

                    <div class="form-floating mt-3 mb-4">
                        <input asp-for="ConfirmPassword" class="form-control" placeholder="••••••••" id="floatingConfirmPassword" />
                        <label asp-for="ConfirmPassword" for="floatingConfirmPassword"><i class="bi bi-shield-lock-fill me-2"></i>Şifre Tekrar</label>
                        <span asp-validation-for="ConfirmPassword" class="text-danger small"></span>
                    </div>

                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary btn-lg">
                            <i class="bi bi-person-plus-fill me-2"></i> Kayıt Ol
                        </button>
                    </div>
                </form>

                <div class="text-center mt-4 pt-3 border-top">
                    <p class="mb-0 small">Zaten bir hesabınız var mı? <a asp-controller="Account" asp-action="Login" class="fw-medium text-decoration-none">Giriş Yapın</a></p>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
} 