<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>@ViewData["Title"] - Modern Apartman Yönetimi</title>

    <!-- Bootstrap 5.3 -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Bootstrap Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css">

    <!-- Google Fonts - Inter for modern typography -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="~/css/site.css" asp-append-version="true" />

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🏢</text></svg>">
</head>
@{
    var isAuthPage = ViewData["IsAuthPage"] as bool? ?? false;
}
<body class="bg-body-tertiary @(isAuthPage ? "auth-page-background" : "")">
    @if (!isAuthPage)
    {
        <header>
            <nav class="navbar navbar-expand-lg fixed-top" id="mainNavbar">
                <div class="container-fluid">
                    <a class="navbar-brand" asp-controller="Home" asp-action="Index">
                        <i class="bi bi-buildings-fill"></i>
                        <span>Apartman Yönetimi</span>
                    </a>

                    <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNavDropdown" aria-controls="navbarNavDropdown" aria-expanded="false" aria-label="Toggle navigation">
                        <span class="navbar-toggler-icon"></span>
                    </button>

                    <div class="collapse navbar-collapse" id="navbarNavDropdown">
                        <ul class="navbar-nav me-auto mb-2 mb-lg-0">
                            @if (User.Identity?.IsAuthenticated == true)
                            {
                                <li class="nav-item">
                                    <a class="nav-link @(ViewContext.RouteData.Values["action"]?.ToString() == "Dashboard" ? "active" : "")" asp-controller="Home" asp-action="Dashboard">
                                        <i class="bi bi-grid-1x2-fill me-2"></i>Dashboard
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link @(ViewContext.RouteData.Values["controller"]?.ToString() == "Dues" ? "active" : "")" asp-controller="Dues" asp-action="Index">
                                        <i class="bi bi-wallet-fill me-2"></i>Aidatlar
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link @(ViewContext.RouteData.Values["controller"]?.ToString() == "Expenses" ? "active" : "")" asp-controller="Expenses" asp-action="Index">
                                        <i class="bi bi-credit-card-2-front-fill me-2"></i>Giderler
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" href="#" onclick="showComingSoon('Raporlar')">
                                        <i class="bi bi-graph-up me-2"></i>Raporlar
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" href="#" onclick="showComingSoon('Duyurular')">
                                        <i class="bi bi-megaphone-fill me-2"></i>Duyurular
                                    </a>
                                </li>
                            }
                        </ul>

                        <ul class="navbar-nav">
                            @if (User.Identity?.IsAuthenticated == true)
                            {
                                <li class="nav-item dropdown">
                                    <a class="nav-link dropdown-toggle d-flex align-items-center" href="#" id="navbarDropdownUser" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                        <div class="user-avatar me-2">
                                            <i class="bi bi-person-circle"></i>
                                        </div>
                                        <span class="d-none d-md-inline">@User.Identity.Name</span>
                                    </a>
                                    <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="navbarDropdownUser">
                                        <li>
                                            <a class="dropdown-item" href="#" onclick="showComingSoon('Profil')">
                                                <i class="bi bi-person-badge-fill me-2"></i>Profilim
                                            </a>
                                        </li>
                                        <li>
                                            <a class="dropdown-item" href="#" onclick="showComingSoon('Ayarlar')">
                                                <i class="bi bi-gear-fill me-2"></i>Ayarlar
                                            </a>
                                        </li>
                                        <li><hr class="dropdown-divider"></li>
                                        <li>
                                            <form asp-controller="Account" asp-action="Logout" method="post" class="d-inline">
                                                <button type="submit" class="dropdown-item text-danger">
                                                    <i class="bi bi-box-arrow-left me-2"></i>Çıkış Yap
                                                </button>
                                            </form>
                                        </li>
                                    </ul>
                                </li>
                            }
                            else
                            {
                                <li class="nav-item">
                                    <a class="nav-link" asp-controller="Account" asp-action="Login">
                                        <i class="bi bi-box-arrow-in-right me-2"></i>Giriş Yap
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link btn btn-primary btn-sm text-white px-3 ms-2" asp-controller="Account" asp-action="Register">
                                        <i class="bi bi-person-plus-fill me-2"></i>Kayıt Ol
                                    </a>
                                </li>
                            }
                        </ul>
                    </div>
                </div>
            </nav>

            <!-- Spacer for fixed navbar -->
            <div style="height: 80px;"></div>
        </header>
    }

    <div class="@(isAuthPage ? "container-fluid auth-container" : "container")">
        <main role="main" class="@(isAuthPage ? "auth-main" : "pb-3")"">
            @if (TempData["Success"] != null && !isAuthPage) // Show alerts only on non-auth pages or handle them differently
            {
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    @TempData["Success"]
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            }
            @if (TempData["Error"] != null && !isAuthPage)
            {
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    @TempData["Error"]
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            }
            @RenderBody()
        </main>
    </div>

    @if (!isAuthPage)
    {
        <footer class="footer mt-auto py-3 bg-white border-top">
            <div class="container text-center">
                <span class="text-muted">&copy; @DateTime.Now.Year - Modern Apartman Yönetim Sistemi. Tüm hakları saklıdır.</span>
            </div>
        </footer>
    }

    <!-- Bootstrap 5.3 JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Custom JavaScript -->
    <script src="~/js/site.js" asp-append-version="true"></script>

    <!-- Modern UI Enhancements -->
    <script>
        // Navbar scroll effect
        window.addEventListener('scroll', function() {
            const navbar = document.getElementById('mainNavbar');
            if (navbar) {
                if (window.scrollY > 50) {
                    navbar.classList.add('scrolled');
                } else {
                    navbar.classList.remove('scrolled');
                }
            }
        });

        // Coming soon modal
        function showComingSoon(feature) {
            alert(`${feature} özelliği yakında eklenecek! 🚀`);
        }

        // Smooth scroll for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Auto-hide alerts after 5 seconds
        setTimeout(function() {
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(alert => {
                const bsAlert = new bootstrap.Alert(alert);
                bsAlert.close();
            });
        }, 5000);
    </script>

    @await RenderSectionAsync("Scripts", required: false)
</body>
</html> 