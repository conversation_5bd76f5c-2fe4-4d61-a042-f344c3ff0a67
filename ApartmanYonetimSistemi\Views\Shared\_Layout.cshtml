<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>@ViewData["Title"] - Modern Apartman Yönetimi</title>
    <link rel="stylesheet" href="~/lib/bootstrap/dist/css/bootstrap.min.css" />
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css">
    <link rel="stylesheet" href="~/css/site.css" asp-append-version="true" />
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
@{
    var isAuthPage = ViewData["IsAuthPage"] as bool? ?? false;
}
<body class="bg-body-tertiary @(isAuthPage ? "auth-page-background" : "")">
    @if (!isAuthPage)
    {
        <header>
            <nav class="navbar navbar-expand-lg navbar-light bg-white shadow-sm mb-4">
                <div class="container-fluid">
                    <a class="navbar-brand fw-bold text-primary" asp-controller="Home" asp-action="Index">
                        <i class="bi bi-buildings-fill me-2"></i>Apartman Yönetimi
                    </a>
                    <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNavDropdown" aria-controls="navbarNavDropdown" aria-expanded="false" aria-label="Toggle navigation">
                        <span class="navbar-toggler-icon"></span>
                    </button>
                    <div class="collapse navbar-collapse" id="navbarNavDropdown">
                        <ul class="navbar-nav me-auto mb-2 mb-lg-0">
                            @if (User.Identity?.IsAuthenticated == true)
                            {
                                <li class="nav-item">
                                    <a class="nav-link" asp-controller="Home" asp-action="Dashboard">
                                        <i class="bi bi-grid-1x2-fill me-1"></i> Dashboard
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" asp-controller="Dues" asp-action="Index">
                                        <i class="bi bi-wallet-fill me-1"></i> Aidatlar
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" asp-controller="Expenses" asp-action="Index">
                                        <i class="bi bi-credit-card-2-front-fill me-1"></i> Giderler
                                    </a>
                                </li>
                            }
                        </ul>
                        <ul class="navbar-nav">
                            @if (User.Identity?.IsAuthenticated == true)
                            {
                                <li class="nav-item dropdown">
                                    <a class="nav-link dropdown-toggle" href="#" id="navbarDropdownUser" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                        <i class="bi bi-person-circle me-1"></i> @User.Identity.Name
                                    </a>
                                    <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="navbarDropdownUser">
                                        <li>
                                            <a class="dropdown-item" asp-controller="Account" asp-action="Profile">
                                                <i class="bi bi-person-badge-fill me-2"></i> Profilim
                                            </a>
                                        </li>
                                        <li><hr class="dropdown-divider"></li>
                                        <li>
                                            <form asp-controller="Account" asp-action="Logout" method="post" class="d-inline">
                                                <button type="submit" class="dropdown-item text-danger">
                                                    <i class="bi bi-box-arrow-left me-2"></i> Çıkış Yap
                                                </button>
                                            </form>
                                        </li>
                                    </ul>
                                </li>
                            }
                            else
                            {
                                <li class="nav-item">
                                    <a class="nav-link" asp-controller="Account" asp-action="Login">
                                        <i class="bi bi-box-arrow-in-right me-1"></i> Giriş Yap
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link btn btn-primary btn-sm text-white px-3" asp-controller="Account" asp-action="Register">
                                        <i class="bi bi-person-plus-fill me-1"></i> Kayıt Ol
                                    </a>
                                </li>
                            }
                        </ul>
                    </div>
                </div>
            </nav>
        </header>
    }

    <div class="container @(isAuthPage ? "auth-container" : "")">
        <main role="main" class="pb-3 @(isAuthPage ? "auth-main" : "")">
            @if (TempData["Success"] != null && !isAuthPage) // Show alerts only on non-auth pages or handle them differently
            {
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    @TempData["Success"]
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            }
            @if (TempData["Error"] != null && !isAuthPage)
            {
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    @TempData["Error"]
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            }
            @RenderBody()
        </main>
    </div>

    @if (!isAuthPage)
    {
        <footer class="footer mt-auto py-3 bg-white border-top">
            <div class="container text-center">
                <span class="text-muted">&copy; @DateTime.Now.Year - Modern Apartman Yönetim Sistemi. Tüm hakları saklıdır.</span>
            </div>
        </footer>
    }

    <script src="~/lib/jquery/dist/jquery.min.js"></script>
    <script src="~/lib/bootstrap/dist/js/bootstrap.bundle.min.js"></script>
    <script src="~/js/site.js" asp-append-version="true"></script>
    @await RenderSectionAsync("Scripts", required: false)
</body>
</html> 