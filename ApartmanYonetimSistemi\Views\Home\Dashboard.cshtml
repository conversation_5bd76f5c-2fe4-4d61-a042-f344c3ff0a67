@{
    ViewData["Title"] = "Dashboard";
}

<div class="container-fluid py-4">
    <div class="row">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="stat-card">
                <div class="stat-title">Toplam Aidat</div>
                <div class="stat-value">₺0.00</div>
                <div class="stat-icon">
                    <i class="bi bi-cash-stack"></i>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="stat-card" style="background: linear-gradient(45deg, #198754, #146c43);">
                <div class="stat-title">Ödenen Aidat</div>
                <div class="stat-value">₺0.00</div>
                <div class="stat-icon">
                    <i class="bi bi-check-circle"></i>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="stat-card" style="background: linear-gradient(45deg, #dc3545, #b02a37);">
                <div class="stat-title">Ödenmemiş Aidat</div>
                <div class="stat-value">₺0.00</div>
                <div class="stat-icon">
                    <i class="bi bi-exclamation-circle"></i>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="stat-card" style="background: linear-gradient(45deg, #6c757d, #495057);">
                <div class="stat-title">Toplam Gider</div>
                <div class="stat-value">₺0.00</div>
                <div class="stat-icon">
                    <i class="bi bi-receipt"></i>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-xl-8 mb-4">
            <div class="card shadow-sm">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Son Aidat Ödemeleri</h5>
                    <a asp-controller="Dues" asp-action="Index" class="btn btn-sm btn-primary">
                        <i class="bi bi-plus"></i> Yeni Aidat
                    </a>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Tarih</th>
                                    <th>Daire</th>
                                    <th>Tutar</th>
                                    <th>Durum</th>
                                    <th>İşlemler</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td colspan="5" class="text-center">Henüz aidat kaydı bulunmamaktadır.</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-4 mb-4">
            <div class="card shadow-sm">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Son Giderler</h5>
                    <a asp-controller="Expenses" asp-action="Index" class="btn btn-sm btn-primary">
                        <i class="bi bi-plus"></i> Yeni Gider
                    </a>
                </div>
                <div class="card-body">
                    <div class="list-group list-group-flush">
                        <div class="list-group-item text-center text-muted">
                            Henüz gider kaydı bulunmamaktadır.
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        // Dashboard için gerekli JavaScript kodları buraya eklenecek
    </script>
} 